<template>
  <div class="experiment-dashboard">
    <!-- 控制面板 -->
    <ControlPanel 
      :current-phase="currentPhase"
      :is-playing="isPlaying"
      :progress="progress"
      @play="startExperiment"
      @pause="pauseExperiment"
      @reset="resetExperiment"
      @phase-change="changePhase"
    />
    
    <!-- 主要布局区域 - 严格按照需求的三栏布局 -->
    <div class="dashboard-layout">
      <!-- 左侧栏 - 纵向三等分 -->
      <div class="left-column">
        <div class="left-top section-item">
          <WaveformChart
            title="一级储能电容组电压"
            :data="voltageData.primary"
            :color="colors.primary"
            unit="V"
            :max-value="1000000"
          />
        </div>
        <div class="left-middle section-item">
          <WaveformChart
            title="中储电容组电压"
            :data="voltageData.intermediate"
            :color="colors.secondary"
            unit="V"
            :max-value="30000000"
          />
        </div>
        <div class="left-bottom section-item">
          <WaveformChart
            title="风化电容组电压"
            :data="voltageData.final"
            :color="colors.accent"
            unit="V"
            :max-value="100000000"
          />
        </div>
      </div>

      <!-- 中间栏 - 上下2:1比例 -->
      <div class="center-column">
        <!-- 中间上部 - 占2/3高度 -->
        <div class="center-top section-item">
          <VideoPlayer
            :video-path="currentVideoPath"
            :is-playing="isPlaying"
            :phase="currentPhase"
          />
        </div>
        
        <!-- 中间下部 - 占1/3高度，左右等分 -->
        <div class="center-bottom">
          <div class="center-bottom-left section-item">
            <PressureDisplay
              :pressure-data="pressureData"
              :gas-data="gasData"
            />
          </div>
          <div class="center-bottom-right section-item">
            <PulseWaveform
              :pulse-data="pulseData"
              :phase="currentPhase"
            />
          </div>
        </div>
      </div>

      <!-- 右侧栏 - 纵向三等分 -->
      <div class="right-column">
        <div class="right-top section-item">
          <WaveformChart
            title="一级储能电容组电流"
            :data="currentData.primary"
            :color="colors.primary"
            unit="A"
            :max-value="10000"
          />
        </div>
        <div class="right-middle section-item">
          <WaveformChart
            title="中储电容组电流"
            :data="currentData.intermediate"
            :color="colors.secondary"
            unit="A"
            :max-value="10000"
          />
        </div>
        <div class="right-bottom section-item">
          <WaveformChart
            title="风化电容组电流"
            :data="currentData.final"
            :color="colors.accent"
            unit="A"
            :max-value="50000"
          />
        </div>
      </div>
    </div>
    
    <!-- 电场建立阶段的特殊布局覆盖 -->
    <div v-if="currentPhase === 'field_establishment'" class="field-establishment-overlay">
      <!-- 电场建立阶段专用布局：左侧电场强度，中间上视频+下Marx波形，右侧实验报告 -->
      <div class="field-overlay-content">
        <!-- 左侧一整列：实时电场强度检测 -->
        <div class="field-left-full section-item">
          <FieldStrengthDisplay :field-data="fieldStrengthData" />
        </div>

        <!-- 中间栏：上2/3视频播放，下1/3 Marx波形 -->
        <div class="field-center-column">
          <!-- 中间上部：3D视频播放窗口 -->
          <div class="field-center-top section-item">
            <VideoPlayer
              :video-path="currentVideoPath"
              :is-playing="isPlaying"
              :phase="currentPhase"
            />
          </div>

          <!-- 中间下部：Marx建立波形 -->
          <div class="field-center-bottom section-item">
            <MarxWaveform :marx-data="marxData" />
          </div>
        </div>

        <!-- 右侧一整列：实验报告摘要 -->
        <div class="field-right-full section-item">
          <ReportSummary :report-data="reportSummary" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG, COLORS } from '../config/experimentConfig.js'
import { 
  generateVoltageData, 
  generateCurrentData, 
  generatePressureData,
  generatePulseData,
  generateFieldStrengthData,
  generateMarxWaveform,
  generateReportSummary
} from '../data/mockData.js'

// 导入子组件
import ControlPanel from './ControlPanel.vue'
import WaveformChart from './WaveformChart.vue'
import VideoPlayer from './VideoPlayer.vue'
import PressureDisplay from './PressureDisplay.vue'
import PulseWaveform from './PulseWaveform.vue'
import FieldStrengthDisplay from './FieldStrengthDisplay.vue'
import ReportSummary from './ReportSummary.vue'
import MarxWaveform from './MarxWaveform.vue'

// 响应式状态
const currentPhase = ref(EXPERIMENT_PHASES.PREPARATION)
const isPlaying = ref(false)
const progress = ref(0)
const colors = COLORS

// 数据状态
const voltageData = reactive({
  primary: [],
  intermediate: [],
  final: []
})

const currentData = reactive({
  primary: [],
  intermediate: [],
  final: []
})

const pressureData = ref([])
const gasData = ref({ type: 'SF4', pressure: 0 })
const pulseData = ref([])
const fieldStrengthData = ref([])
const marxData = ref([])
const reportSummary = ref({})

// 计算属性
const currentVideoPath = computed(() => {
  return PHASE_CONFIG[currentPhase.value]?.videoPath || null
})

// 定时器
let phaseTimer = null
let dataUpdateTimer = null

// 实验控制方法
const startExperiment = () => {
  isPlaying.value = true
  updatePhaseData()
  startPhaseTimer()
}

const pauseExperiment = () => {
  isPlaying.value = false
  clearTimers()
}

const resetExperiment = () => {
  isPlaying.value = false
  currentPhase.value = EXPERIMENT_PHASES.PREPARATION
  progress.value = 0
  clearTimers()
  updatePhaseData()
}

const changePhase = (phase) => {
  currentPhase.value = phase
  progress.value = 0
  updatePhaseData()
  if (isPlaying.value) {
    startPhaseTimer()
  }
}

// 更新阶段数据
const updatePhaseData = () => {
  const phase = currentPhase.value
  const currentProgress = progress.value / 100 // 转换为0-1之间的值

  // 更新电压数据
  voltageData.primary = generateVoltageData('PRIMARY', phase, currentProgress)
  voltageData.intermediate = generateVoltageData('INTERMEDIATE', phase, currentProgress)
  voltageData.final = generateVoltageData('FINAL', phase, currentProgress)

  // 更新电流数据
  currentData.primary = generateCurrentData('PRIMARY', phase, currentProgress)
  currentData.intermediate = generateCurrentData('INTERMEDIATE', phase, currentProgress)
  currentData.final = generateCurrentData('FINAL', phase, currentProgress)

  // 更新其他数据
  pressureData.value = generatePressureData(phase, currentProgress)
  pulseData.value = generatePulseData(phase, currentProgress)

  if (phase === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT) {
    fieldStrengthData.value = generateFieldStrengthData(currentProgress)
    marxData.value = generateMarxWaveform(currentProgress)
    reportSummary.value = generateReportSummary()
  }
}

// 启动阶段计时器
const startPhaseTimer = () => {
  clearTimers()

  const phaseDuration = PHASE_CONFIG[currentPhase.value]?.duration
  if (!phaseDuration) {
    // 如果没有持续时间，直接更新数据并停止
    updatePhaseData()
    return
  }

  const startTime = Date.now()

  const updateProgress = () => {
    const elapsed = Date.now() - startTime
    progress.value = Math.min((elapsed / phaseDuration) * 100, 100)

    // 根据当前进度更新数据
    updatePhaseData()

    if (progress.value >= 100) {
      // 自动进入下一阶段
      const phases = Object.values(EXPERIMENT_PHASES)
      const currentIndex = phases.indexOf(currentPhase.value)
      if (currentIndex < phases.length - 1) {
        currentPhase.value = phases[currentIndex + 1]
        progress.value = 0
        updatePhaseData()
        startPhaseTimer()
      } else {
        // 电场建立阶段完成后，继续更新数据但不进入下一阶段
        if (currentPhase.value === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT) {
          startFieldEstablishmentTimer()
        } else {
          isPlaying.value = false
        }
      }
    } else {
      phaseTimer = requestAnimationFrame(updateProgress)
    }
  }

  phaseTimer = requestAnimationFrame(updateProgress)
}

// 电场建立阶段的持续更新计时器
const startFieldEstablishmentTimer = () => {
  const updateFieldData = () => {
    if (currentPhase.value === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT && isPlaying.value) {
      updatePhaseData()
      phaseTimer = requestAnimationFrame(updateFieldData)
    }
  }
  phaseTimer = requestAnimationFrame(updateFieldData)
}

// 清除定时器
const clearTimers = () => {
  if (phaseTimer) {
    cancelAnimationFrame(phaseTimer)
    phaseTimer = null
  }
  if (dataUpdateTimer) {
    clearInterval(dataUpdateTimer)
    dataUpdateTimer = null
  }
}

// 生命周期
onMounted(() => {
  updatePhaseData()
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style scoped>
.experiment-dashboard {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 2px;
  gap: 2px;
  height: calc(100vh - 54px);
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  left: 0;
  top: 0;
}

.dashboard-layout {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 2fr) minmax(0, 1fr);
  gap: 2px;
  flex: 1;
  min-height: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  left: 0;
  top: 0;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 0;
  height: 100%;
  width: 100%;
}



.left-column > .section-item,
.right-column > .section-item {
  flex: 1;
  min-height: 0;
  height: calc((100% - 16px) / 3);
}

.center-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 0;
  height: 100%;
  width: 100%;
}

.center-top {
  flex: 2;
  min-height: 0;
  height: calc((100% - 8px) * 2 / 3);
}

.center-bottom {
  flex: 1;
  display: flex;
  gap: 4px;
  min-height: 0;
  height: calc((100% - 4px) / 3);
}

.center-bottom-left,
.center-bottom-right {
  flex: 1;
  min-height: 0;
  width: calc((100% - 4px) / 2);
}

.section-item {
  background: linear-gradient(135deg, rgba(10, 25, 46, 0.9) 0%, rgba(15, 35, 60, 0.8) 100%);
  border: 2px solid transparent;
  border-radius: 6px;
  padding: 8px;
  box-shadow:
    0 0 20px rgba(0, 240, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.section-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  padding: 2px;
  background: linear-gradient(45deg, #00F0FF, #39FF14, #18FFFF, #7C4DFF);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.section-item:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 32px rgba(0, 240, 255, 0.4),
    0 0 40px rgba(57, 255, 20, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.section-item:hover {
  border-color: #39FF14;
  box-shadow: 0 0 30px rgba(57, 255, 20, 0.3);
  transform: translateY(-2px);
}

.field-establishment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 25, 46, 0.98);
  z-index: 100;
  backdrop-filter: blur(10px);
}

.field-overlay-content {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 8px;
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
}

.field-left-full,
.field-right-full {
  width: 100%;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid rgba(0, 240, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.1);
}

.field-center-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 0;
  height: 100%;
  width: 100%;
}

.field-center-top {
  flex: 2;
  min-height: 0;
  height: calc((100% - 8px) * 2 / 3);
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid rgba(0, 240, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.1);
}

.field-center-bottom {
  flex: 1;
  min-height: 0;
  height: calc((100% - 8px) / 3);
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid rgba(24, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(24, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 6px;
  }
  
  .left-column,
  .right-column,
  .center-column {
    height: auto;
    min-height: 300px;
  }
  
  .center-bottom {
    flex-direction: column;
  }
  
  .field-overlay-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 8px;
  }

  .field-left-full,
  .field-right-full {
    height: auto;
    min-height: 300px;
  }

  .field-center-column {
    height: auto;
    min-height: 400px;
  }

  .field-center-top {
    height: 300px;
  }

  .field-center-bottom {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .experiment-dashboard {
    padding: 4px;
    gap: 4px;
  }
  
  .dashboard-layout {
    gap: 4px;
  }
  
  .section-item {
    padding: 8px;
    border-radius: 6px;
  }
  
  .left-column > .section-item,
  .right-column > .section-item {
    height: auto;
    min-height: 150px;
  }
}
</style>

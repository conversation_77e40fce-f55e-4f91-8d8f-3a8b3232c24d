<template>
  <div class="test-component">
    <h2>组件测试</h2>
    <div class="test-grid">
      <div class="test-item">
        <h3>配置测试</h3>
        <p>当前阶段: {{ currentPhase }}</p>
        <p>颜色配置: {{ colors.primary }}</p>
      </div>
      
      <div class="test-item">
        <h3>数据生成测试</h3>
        <p>电压数据点数: {{ voltageData.length }}</p>
        <p>电流数据点数: {{ currentData.length }}</p>
      </div>
      
      <div class="test-item">
        <h3>ECharts测试</h3>
        <div class="chart-container" ref="chartContainer"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { EXPERIMENT_PHASES, COLORS } from '../config/experimentConfig.js'
import { generateVoltageData, generateCurrentData } from '../data/mockData.js'

const chartContainer = ref(null)
const currentPhase = ref(EXPERIMENT_PHASES.PREPARATION)
const colors = COLORS
const voltageData = ref([])
const currentData = ref([])

let chartInstance = null

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    xAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#00F0FF' } },
      axisLabel: { color: '#00F0FF' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#00F0FF' } },
      axisLabel: { color: '#00F0FF' }
    },
    series: [{
      type: 'line',
      data: voltageData.value.map(item => [parseFloat(item.time), parseFloat(item.value)]),
      lineStyle: { color: '#39FF14' }
    }]
  }
  
  chartInstance.setOption(option)
}

onMounted(() => {
  // 生成测试数据
  voltageData.value = generateVoltageData('PRIMARY', currentPhase.value)
  currentData.value = generateCurrentData('PRIMARY', currentPhase.value)
  
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.test-component {
  padding: 20px;
  color: #fff;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.test-item {
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid #00F0FF;
  border-radius: 8px;
  padding: 15px;
}

.chart-container {
  height: 200px;
  margin-top: 10px;
}

h2, h3 {
  color: #00F0FF;
  margin-bottom: 10px;
}

p {
  margin: 5px 0;
  color: #ccc;
}
</style>

// 实验配置文件
export const EXPERIMENT_PHASES = {
  PREPARATION: 'preparation',
  PRIMARY_CHARGING: 'primary_charging', 
  INTERMEDIATE_CHARGING: 'intermediate_charging',
  FINAL_CHARGING: 'final_charging',
  FIELD_ESTABLISHMENT: 'field_establishment'
}

export const PHASE_CONFIG = {
  [EXPERIMENT_PHASES.PREPARATION]: {
    name: '准备阶段',
    duration: 20000, // 20秒
    videoPath: '/videos/preparation.mp4',
    description: '实验装备介绍和初始状态'
  },
  [EXPERIMENT_PHASES.PRIMARY_CHARGING]: {
    name: '一级充能阶段',
    duration: 15000, // 15秒
    videoPath: '/videos/primary_charging.mp4',
    description: '一级储能电容组充电'
  },
  [EXPERIMENT_PHASES.INTERMEDIATE_CHARGING]: {
    name: '中储电容充能阶段',
    duration: 10000, // 10秒
    videoPath: '/videos/intermediate_charging.mp4',
    description: '中储电容组充电'
  },
  [EXPERIMENT_PHASES.FINAL_CHARGING]: {
    name: '风化电容充能阶段',
    duration: 8000, // 8秒
    videoPath: '/videos/final_charging.mp4',
    description: '风化电容组充电'
  },
  [EXPERIMENT_PHASES.FIELD_ESTABLISHMENT]: {
    name: '电场建立阶段',
    duration: 0, // 持续显示
    videoPath: null,
    description: '电场强度监测和数据报告'
  }
}

// 颜色配置
export const COLORS = {
  primary: '#00F0FF',      // 荧光蓝
  secondary: '#39FF14',    // 荧光绿
  accent: '#18FFFF',       // 青色
  purple: '#7C4DFF',       // 紫色
  magenta: '#E91E63',      // 洋红
  lime: '#CCFF00',         // 荧光黄绿
  cyan: '#00FFFF',         // 纯青色
  violet: '#8A2BE2',       // 蓝紫色
  warning: '#FFC400',      // 高亮黄
  background: '#0A192E',   // 深色背景
  cardBg: 'rgba(10, 25, 46, 0.8)',
  borderGlow: 'rgba(0, 240, 255, 0.5)',
  gradients: {
    primary: 'linear-gradient(45deg, #00F0FF, #39FF14)',
    secondary: 'linear-gradient(45deg, #18FFFF, #7C4DFF)',
    accent: 'linear-gradient(45deg, #E91E63, #CCFF00)',
    rainbow: 'linear-gradient(45deg, #00F0FF, #39FF14, #18FFFF, #7C4DFF, #E91E63)',
    glow: 'radial-gradient(circle, rgba(0,240,255,0.3) 0%, rgba(57,255,20,0.1) 50%, transparent 100%)'
  }
}

// 布局配置
export const LAYOUT = {
  leftWidth: '25%',
  centerWidth: '50%', 
  rightWidth: '25%',
  topHeight: '66.67%',
  bottomHeight: '33.33%'
}

// 图表通用配置
export const CHART_CONFIG = {
  backgroundColor: 'transparent',
  textStyle: {
    color: COLORS.primary,
    fontFamily: 'Arial, sans-serif'
  },
  grid: {
    left: '10%',
    right: '10%',
    top: '15%',
    bottom: '15%',
    borderColor: COLORS.borderGlow
  },
  xAxis: {
    type: 'value',
    name: '时间(s)',
    nameTextStyle: { color: COLORS.primary },
    axisLine: { lineStyle: { color: COLORS.primary } },
    axisTick: { lineStyle: { color: COLORS.primary } },
    axisLabel: { color: COLORS.primary }
  },
  yAxis: {
    type: 'value',
    nameTextStyle: { color: COLORS.primary },
    axisLine: { lineStyle: { color: COLORS.primary } },
    axisTick: { lineStyle: { color: COLORS.primary } },
    axisLabel: { color: COLORS.primary }
  }
}

// 气体类型配置
export const GAS_TYPES = {
  SF4: { name: 'SF4', color: COLORS.secondary },
  N2: { name: 'N2', color: COLORS.accent }
}

// 电容组配置
export const CAPACITOR_GROUPS = {
  PRIMARY: { name: '一级储能电容组', maxVoltage: 1000000, maxCurrent: 10000 },
  INTERMEDIATE: { name: '中储电容组', maxVoltage: 30000000, maxCurrent: 10000 },
  FINAL: { name: '风化电容组', maxVoltage: 100000000, maxCurrent: 50000 }
}

<template>
  <div class="report-summary">
    <div class="summary-header">
      <h3 class="summary-title">实验报告摘要</h3>
      <div class="report-status">
        <span class="status-indicator"></span>
        <span class="status-text">数据采集完成</span>
      </div>
    </div>
    
    <div class="summary-content">
      <!-- 关键参数卡片 -->
      <div class="parameter-cards">
        <div class="param-card primary">
          <div class="card-icon">⚡</div>
          <div class="card-content">
            <div class="card-label">电场方向</div>
            <div class="card-value">{{ reportData.direction }}</div>
          </div>
        </div>
        
        <div class="param-card secondary">
          <div class="card-icon">📐</div>
          <div class="card-content">
            <div class="card-label">作用面积</div>
            <div class="card-value">{{ reportData.area }}</div>
          </div>
        </div>
        
        <div class="param-card accent">
          <div class="card-icon">⏱️</div>
          <div class="card-content">
            <div class="card-label">上升时间</div>
            <div class="card-value">{{ reportData.riseTime }}</div>
          </div>
        </div>
        
        <div class="param-card warning">
          <div class="card-icon">📊</div>
          <div class="card-content">
            <div class="card-label">脉冲宽度</div>
            <div class="card-value">{{ reportData.pulseWidth }}</div>
          </div>
        </div>
      </div>
      
      <!-- 核心数据展示 -->
      <div class="core-data">
        <div class="data-section">
          <div class="section-title">电场强度参数</div>
          <div class="data-grid">
            <div class="data-item">
              <div class="data-label">平均幅值</div>
              <div class="data-value highlight">{{ reportData.amplitude }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">峰值强度</div>
              <div class="data-value peak">{{ reportData.peakValue }}</div>
            </div>
            <div class="data-item">
              <div class="data-label">峰值时刻</div>
              <div class="data-value">{{ reportData.peakTime }}</div>
            </div>
          </div>
        </div>
        
        <div class="data-section">
          <div class="section-title">实验评估</div>
          <div class="evaluation-items">
            <div class="eval-item">
              <div class="eval-label">实验状态</div>
              <div class="eval-value success">✓ 成功完成</div>
            </div>
            <div class="eval-item">
              <div class="eval-label">数据质量</div>
              <div class="eval-value success">✓ 优秀</div>
            </div>
            <div class="eval-item">
              <div class="eval-label">安全等级</div>
              <div class="eval-value success">✓ 正常</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 数据统计图表 -->
      <div class="statistics-chart">
        <div class="chart-title">电场强度分布统计</div>
        <div class="chart-container" ref="chartContainer"></div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="summary-actions">
      <button class="action-btn export-btn" @click="exportReport">
        <span class="btn-icon">📄</span>
        <span class="btn-text">导出报告</span>
      </button>
      <button class="action-btn print-btn" @click="printReport">
        <span class="btn-icon">🖨️</span>
        <span class="btn-text">打印报告</span>
      </button>
      <button class="action-btn save-btn" @click="saveReport">
        <span class="btn-icon">💾</span>
        <span class="btn-text">保存数据</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  reportData: {
    type: Object,
    default: () => ({
      direction: '垂直向上',
      area: '1.2 m²',
      riseTime: '2.3 μs',
      pulseWidth: '15.7 μs',
      amplitude: '85.6 MV/m',
      peakValue: '92.1 MV/m',
      peakTime: '8.9 μs'
    })
  }
})

// 响应式数据
const chartContainer = ref(null)
let chartInstance = null

// 方法
const exportReport = () => {
  // 导出报告逻辑
  console.log('导出报告')
  // 这里可以实现PDF导出或其他格式导出
}

const printReport = () => {
  // 打印报告逻辑
  console.log('打印报告')
  window.print()
}

const saveReport = () => {
  // 保存数据逻辑
  console.log('保存数据')
  // 这里可以实现数据保存到本地或服务器
}

// 初始化统计图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  // 模拟电场强度分布数据
  const distributionData = [
    { name: '0-20 MV/m', value: 15 },
    { name: '20-40 MV/m', value: 25 },
    { name: '40-60 MV/m', value: 30 },
    { name: '60-80 MV/m', value: 20 },
    { name: '80-100 MV/m', value: 10 }
  ]
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: '#00F0FF',
      textStyle: { color: '#fff' }
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: distributionData,
      itemStyle: {
        borderRadius: 5,
        borderColor: '#0A192E',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'outside',
        color: '#fff',
        fontSize: 10
      },
      labelLine: {
        show: true,
        lineStyle: {
          color: '#00F0FF'
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 240, 255, 0.5)'
        }
      }
    }],
    color: ['#00F0FF', '#39FF14', '#FFC400', '#FF6B35', '#7C4DFF']
  }
  
  chartInstance.setOption(option)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.report-summary {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid #39FF14;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(57, 255, 20, 0.2);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(57, 255, 20, 0.3);
}

.summary-title {
  font-size: 18px;
  font-weight: bold;
  color: #39FF14;
  margin: 0;
}

.report-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #39FF14;
  box-shadow: 0 0 10px #39FF14;
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.status-text {
  font-size: 12px;
  color: #39FF14;
  font-weight: bold;
}

.summary-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.parameter-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.param-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.param-card.primary {
  background: linear-gradient(135deg, rgba(0, 240, 255, 0.1), rgba(0, 240, 255, 0.05));
  border: 1px solid rgba(0, 240, 255, 0.3);
}

.param-card.secondary {
  background: linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(57, 255, 20, 0.05));
  border: 1px solid rgba(57, 255, 20, 0.3);
}

.param-card.accent {
  background: linear-gradient(135deg, rgba(255, 196, 0, 0.1), rgba(255, 196, 0, 0.05));
  border: 1px solid rgba(255, 196, 0, 0.3);
}

.param-card.warning {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
  border: 1px solid rgba(255, 107, 53, 0.3);
}

.param-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.card-icon {
  font-size: 24px;
  opacity: 0.8;
}

.card-content {
  flex: 1;
}

.card-label {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 3px;
}

.card-value {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}

.core-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.data-section {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #18FFFF;
  margin-bottom: 15px;
  text-align: center;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-size: 12px;
  color: #ccc;
}

.data-value {
  font-size: 13px;
  font-weight: bold;
  color: #fff;
}

.data-value.highlight {
  color: #FFC400;
  text-shadow: 0 0 5px rgba(255, 196, 0, 0.5);
}

.data-value.peak {
  color: #FF4444;
  text-shadow: 0 0 5px rgba(255, 68, 68, 0.5);
}

.evaluation-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.eval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.eval-label {
  font-size: 12px;
  color: #ccc;
}

.eval-value.success {
  font-size: 12px;
  color: #39FF14;
  font-weight: bold;
}

.statistics-chart {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #18FFFF;
  margin-bottom: 10px;
  text-align: center;
}

.chart-container {
  height: 200px;
}

.summary-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(57, 255, 20, 0.3);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: bold;
}

.export-btn {
  background: linear-gradient(45deg, #00F0FF, #18FFFF);
  color: #0A192E;
}

.print-btn {
  background: linear-gradient(45deg, #39FF14, #7C4DFF);
  color: #0A192E;
}

.save-btn {
  background: linear-gradient(45deg, #FFC400, #FF6B35);
  color: #0A192E;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .parameter-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .core-data {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .parameter-cards {
    grid-template-columns: 1fr;
  }
  
  .summary-actions {
    flex-direction: column;
  }
}
</style>

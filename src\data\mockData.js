// Mock数据生成器
import { EXPERIMENT_PHASES, CAPACITOR_GROUPS } from '../config/experimentConfig.js'

// 生成波形数据的工具函数 - 支持进度参数
export function generateWaveform(duration, maxValue, waveType = 'linear', noise = 0.1, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100) // 每100ms一个数据点
  // 根据进度计算当前应该生成的点数
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000) // 转换为秒
    let value = 0

    switch (waveType) {
      case 'linear':
        value = (i / steps) * maxValue
        break
      case 'exponential':
        value = maxValue * (1 - Math.exp(-3 * i / steps))
        break
      case 'decay':
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'pulse':
        value = i === Math.floor(steps * 0.1) ? maxValue : 0
        break
      case 'sine':
        value = maxValue * Math.sin(2 * Math.PI * i / steps)
        break
      case 'static':
        value = maxValue + (Math.random() - 0.5) * noise * maxValue
        break
    }

    // 添加噪声
    if (noise > 0 && waveType !== 'static') {
      value += (Math.random() - 0.5) * noise * maxValue
    }

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成自定义波形数据 - 用于特殊的放电过程
function generateCustomWaveform(duration, maxValue, waveType, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    let value = 0

    switch (waveType) {
      case 'discharge_to_intermediate':
        // 一级电容向中储电容放电：从100%衰减到20%
        value = maxValue * (0.2 + 0.8 * Math.exp(-2 * i / steps))
        break
      case 'discharge_to_final':
        // 中储电容向风化电容放电：从100%衰减到15%
        value = maxValue * (0.15 + 0.85 * Math.exp(-2.5 * i / steps))
        break
      case 'discharge_current_to_intermediate':
        // 一级电容放电电流：随电压降低而降低，更快衰减
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'discharge_current_to_final':
        // 中储电容放电电流：随电压降低而降低，更快衰减
        value = maxValue * Math.exp(-3.5 * i / steps)
        break
      default:
        value = maxValue * Math.exp(-3 * i / steps)
    }

    // 添加小幅噪声
    value += (Math.random() - 0.5) * 0.02 * maxValue

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成气压数据 - 只在准备阶段最后10秒充气
export function generatePressureData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      // 准备阶段：前10秒保持低压，后10秒充气到2MPa
      const points = []
      const steps = Math.floor(20000 / 100) // 200个点，每100ms一个
      const currentSteps = Math.floor(steps * progress)

      for (let i = 0; i <= currentSteps; i++) {
        const time = (i / steps) * 20 // 20秒总时长
        let value = 0

        if (time < 10) {
          // 前10秒：保持低压（约0.1MPa）
          value = 100000 + (Math.random() - 0.5) * 10000
        } else {
          // 后10秒：充气过程，指数增长到2MPa
          const chargeProgress = (time - 10) / 10 // 0-1
          value = 100000 + 1900000 * (1 - Math.exp(-3 * chargeProgress))
          value += (Math.random() - 0.5) * 50000 // 充气过程的噪声
        }

        points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
      }
      return points

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      // 后续阶段：保持2MPa，只是测量，有小幅波动
      return generateWaveform(
        phase === EXPERIMENT_PHASES.PRIMARY_CHARGING ? 15000 :
        phase === EXPERIMENT_PHASES.INTERMEDIATE_CHARGING ? 10000 : 8000,
        2000000, 'static', 0.02, progress
      )

    default:
      return generateWaveform(5000, 2000000, 'static', 0.02, progress)
  }
}

// 生成电压数据 - 考虑电容器间的能量转移
export function generateVoltageData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        return generateWaveform(15000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(15000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容放电：从满电压衰减到约20%
        return generateCustomWaveform(10000, config.maxVoltage, 'discharge_to_intermediate', progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容充电：从0充到满电压
        return generateWaveform(10000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容保持低电压
        return generateWaveform(8000, config.maxVoltage * 0.2, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容放电：从满电压衰减到约15%
        return generateCustomWaveform(8000, config.maxVoltage, 'discharge_to_final', progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容充电：从0充到满电压
        return generateWaveform(8000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成电流数据 - 放电电流随电压降低而降低
export function generateCurrentData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 充电电流：开始大，逐渐减小
        return generateWaveform(15000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(15000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容放电电流：随电压降低而降低
        return generateCustomWaveform(10000, config.maxCurrent, 'discharge_current_to_intermediate', progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容充电电流：开始大，逐渐减小
        return generateWaveform(10000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容基本无电流
        return generateWaveform(8000, config.maxCurrent * 0.1, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容放电电流：随电压降低而降低
        return generateCustomWaveform(8000, config.maxCurrent, 'discharge_current_to_final', progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容充电电流：开始大，逐渐减小
        return generateWaveform(8000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成脉冲波形数据 - 开关导通时的瞬间脉冲
export function generatePulseData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      // 中储充能阶段：开关在开始时导通，产生瞬间脉冲
      return generateSwitchPulse(10000, 100000, 'intermediate_switch', progress)
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      // 风化充能阶段：开关在开始时导通，产生瞬间脉冲
      return generateSwitchPulse(8000, 10000000, 'final_switch', progress)
    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成开关脉冲数据 - 瞬间脉冲，不需要动态变化
function generateSwitchPulse(duration, maxValue, switchType, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    let value = 0

    // 开关脉冲只在最开始的几个时间点出现
    if (i <= 2) { // 前200ms内的脉冲
      switch (switchType) {
        case 'intermediate_switch':
          value = i === 1 ? maxValue : 0 // 在第100ms时出现脉冲
          break
        case 'final_switch':
          value = i === 1 ? maxValue : 0 // 在第100ms时出现脉冲
          break
      }
    } else {
      value = 0 // 脉冲后保持为0
    }

    // 脉冲不需要噪声，保持干净的信号
    points.push({ time: time.toFixed(2), value: value.toFixed(2) })
  }

  return points
}

// 生成电场强度数据（多通道）
export function generateFieldStrengthData(progress = 1) {
  const channels = ['A', 'B', 'C', 'D']
  return channels.map(channel => ({
    channel,
    data: generateWaveform(5000, Math.random() * 1000000, 'sine', 0.1, progress)
  }))
}

// 生成Marx建立波形数据
export function generateMarxWaveform(progress = 1) {
  const channels = Array.from({ length: 8 }, (_, i) => `通道${i + 1}`)
  return channels.map(channel => ({
    channel,
    data: generateWaveform(5000, Math.random() * 50000000, 'exponential', 0.05, progress),
    visible: true
  }))
}

// 生成信息报告摘要
export function generateReportSummary() {
  return {
    direction: '垂直向上',
    area: '1.2 m²',
    riseTime: '2.3 μs',
    pulseWidth: '15.7 μs', 
    amplitude: '85.6 MV/m',
    peakValue: '92.1 MV/m',
    peakTime: '8.9 μs'
  }
}

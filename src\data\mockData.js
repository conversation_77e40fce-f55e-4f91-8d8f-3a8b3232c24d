// Mock数据生成器
import { EXPERIMENT_PHASES, CAPACITOR_GROUPS } from '../config/experimentConfig.js'

// 生成波形数据的工具函数 - 支持进度参数
export function generateWaveform(duration, maxValue, waveType = 'linear', noise = 0.1, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100) // 每100ms一个数据点
  // 根据进度计算当前应该生成的点数
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000) // 转换为秒
    let value = 0

    switch (waveType) {
      case 'linear':
        value = (i / steps) * maxValue
        break
      case 'exponential':
        value = maxValue * (1 - Math.exp(-3 * i / steps))
        break
      case 'decay':
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'pulse':
        value = i === Math.floor(steps * 0.1) ? maxValue : 0
        break
      case 'sine':
        value = maxValue * Math.sin(2 * Math.PI * i / steps)
        break
      case 'static':
        value = maxValue + (Math.random() - 0.5) * noise * maxValue
        break
    }

    // 添加噪声
    if (noise > 0 && waveType !== 'static') {
      value += (Math.random() - 0.5) * noise * maxValue
    }

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成气压数据
export function generatePressureData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)
    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      return generateWaveform(15000, 2000000, 'exponential', 0.05, progress) // 2MPa
    default:
      return generateWaveform(10000, 2000000, 'static', 0.02, progress)
  }
}

// 生成电压数据
export function generateVoltageData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        return generateWaveform(15000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(15000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        return generateWaveform(10000, 0, 'decay', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        return generateWaveform(10000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'INTERMEDIATE') {
        return generateWaveform(8000, 0, 'decay', 0.02, progress)
      } else if (capacitorType === 'FINAL') {
        return generateWaveform(8000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成电流数据
export function generateCurrentData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        return generateWaveform(15000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(15000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        return generateWaveform(10000, config.maxCurrent, 'pulse', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        return generateWaveform(10000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'INTERMEDIATE') {
        return generateWaveform(8000, config.maxCurrent, 'pulse', 0.02, progress)
      } else if (capacitorType === 'FINAL') {
        return generateWaveform(8000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成脉冲波形数据
export function generatePulseData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      return generateWaveform(10000, 100000, 'pulse', 0.1, progress) // 10万伏
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      return generateWaveform(8000, 10000000, 'pulse', 0.1, progress) // 1000万伏
    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成电场强度数据（多通道）
export function generateFieldStrengthData(progress = 1) {
  const channels = ['A', 'B', 'C', 'D']
  return channels.map(channel => ({
    channel,
    data: generateWaveform(5000, Math.random() * 1000000, 'sine', 0.1, progress)
  }))
}

// 生成Marx建立波形数据
export function generateMarxWaveform(progress = 1) {
  const channels = Array.from({ length: 8 }, (_, i) => `通道${i + 1}`)
  return channels.map(channel => ({
    channel,
    data: generateWaveform(5000, Math.random() * 50000000, 'exponential', 0.05, progress),
    visible: true
  }))
}

// 生成信息报告摘要
export function generateReportSummary() {
  return {
    direction: '垂直向上',
    area: '1.2 m²',
    riseTime: '2.3 μs',
    pulseWidth: '15.7 μs', 
    amplitude: '85.6 MV/m',
    peakValue: '92.1 MV/m',
    peakTime: '8.9 μs'
  }
}

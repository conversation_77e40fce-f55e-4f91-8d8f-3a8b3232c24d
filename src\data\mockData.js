// Mock数据生成器
import { EXPERIMENT_PHASES, CAPACITOR_GROUPS } from '../config/experimentConfig.js'

// 生成波形数据的工具函数 - 支持进度参数
export function generateWaveform(duration, maxValue, waveType = 'linear', noise = 0.1, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100) // 每100ms一个数据点
  // 根据进度计算当前应该生成的点数
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000) // 转换为秒
    let value = 0

    switch (waveType) {
      case 'linear':
        value = (i / steps) * maxValue
        break
      case 'exponential':
        value = maxValue * (1 - Math.exp(-3 * i / steps))
        break
      case 'decay':
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'pulse':
        value = i === Math.floor(steps * 0.1) ? maxValue : 0
        break
      case 'sine':
        value = maxValue * Math.sin(2 * Math.PI * i / steps)
        break
      case 'static':
        value = maxValue + (Math.random() - 0.5) * noise * maxValue
        break
    }

    // 添加噪声
    if (noise > 0 && waveType !== 'static') {
      value += (Math.random() - 0.5) * noise * maxValue
    }

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成自定义波形数据 - 用于特殊的放电过程
function generateCustomWaveform(duration, maxValue, waveType, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    let value = 0

    switch (waveType) {
      case 'discharge_to_intermediate':
        // 一级电容向中储电容放电：从100%衰减到20%
        value = maxValue * (0.2 + 0.8 * Math.exp(-2 * i / steps))
        break
      case 'discharge_to_final':
        // 中储电容向风化电容放电：从100%衰减到15%
        value = maxValue * (0.15 + 0.85 * Math.exp(-2.5 * i / steps))
        break
      case 'discharge_current_to_intermediate':
        // 一级电容放电电流：随电压降低而降低，更快衰减
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'discharge_current_to_final':
        // 中储电容放电电流：随电压降低而降低，更快衰减
        value = maxValue * Math.exp(-3.5 * i / steps)
        break
      case 'discharge_to_antenna':
        // 风化电容向天线放电：快速衰减到接近0
        value = maxValue * Math.exp(-4 * i / steps)
        break
      case 'discharge_current_to_antenna':
        // 风化电容向天线放电电流：非常快速衰减
        value = maxValue * Math.exp(-5 * i / steps)
        break
      default:
        value = maxValue * Math.exp(-3 * i / steps)
    }

    // 添加小幅噪声
    value += (Math.random() - 0.5) * 0.02 * maxValue

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成气压数据 - 只在准备阶段最后10秒充气
export function generatePressureData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      // 准备阶段：前10秒气压为0（未充气），后10秒充气到2MPa
      const points = []
      const steps = Math.floor(20000 / 100) // 200个点，每100ms一个
      const currentSteps = Math.floor(steps * progress)

      for (let i = 0; i <= currentSteps; i++) {
        const time = (i / steps) * 20 // 20秒总时长
        let value = 0

        if (time < 10) {
          // 前10秒：气压为0，只有微小的检测噪声
          value = (Math.random() - 0.5) * 1000 // 很小的噪声，接近0
        } else {
          // 后10秒：充气过程，指数增长到2MPa
          const chargeProgress = (time - 10) / 10 // 0-1
          value = 1900000 * (1 - Math.exp(-3 * chargeProgress))
          value += (Math.random() - 0.5) * 50000 // 充气过程的噪声
        }

        points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
      }
      return points

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      // 后续阶段：保持2MPa，只是测量，有小幅波动
      return generateWaveform(
        phase === EXPERIMENT_PHASES.PRIMARY_CHARGING ? 15000 :
        phase === EXPERIMENT_PHASES.INTERMEDIATE_CHARGING ? 10000 : 8000,
        2000000, 'static', 0.02, progress
      )

    default:
      return generateWaveform(5000, 2000000, 'static', 0.02, progress)
  }
}

// 生成电压数据 - 考虑电容器间的能量转移
export function generateVoltageData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        return generateWaveform(15000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(15000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容放电：从满电压衰减到约20%
        return generateCustomWaveform(10000, config.maxVoltage, 'discharge_to_intermediate', progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容充电：从0充到满电压
        return generateWaveform(10000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容保持低电压
        return generateWaveform(8000, config.maxVoltage * 0.2, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容放电：从满电压衰减到约15%
        return generateCustomWaveform(8000, config.maxVoltage, 'discharge_to_final', progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容充电：从0充到满电压
        return generateWaveform(8000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FIELD_ESTABLISHMENT:
      if (capacitorType === 'PRIMARY') {
        // 一级电容保持低电压
        return generateWaveform(5000, config.maxVoltage * 0.2, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容保持低电压
        return generateWaveform(5000, config.maxVoltage * 0.15, 'static', 0.02, progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容放电到天线：快速衰减
        return generateCustomWaveform(5000, config.maxVoltage, 'discharge_to_antenna', progress)
      }
      return generateWaveform(5000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成电流数据 - 放电电流随电压降低而降低
export function generateCurrentData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 充电电流：开始大，逐渐减小
        return generateWaveform(15000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(15000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容放电电流：随电压降低而降低
        return generateCustomWaveform(10000, config.maxCurrent, 'discharge_current_to_intermediate', progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容充电电流：开始大，逐渐减小
        return generateWaveform(10000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容基本无电流
        return generateWaveform(8000, config.maxCurrent * 0.1, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容放电电流：随电压降低而降低
        return generateCustomWaveform(8000, config.maxCurrent, 'discharge_current_to_final', progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容充电电流：开始大，逐渐减小
        return generateWaveform(8000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成脉冲波形数据 - 开关导通时的瞬间脉冲
export function generatePulseData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      // 中储充能阶段：开关在开始时导通，产生瞬间脉冲
      return generateSwitchPulse(10000, 100000, 'intermediate_switch', progress)
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      // 风化充能阶段：开关在开始时导通，产生瞬间脉冲
      return generateSwitchPulse(8000, 10000000, 'final_switch', progress)
    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成开关脉冲数据 - 瞬间脉冲，不需要动态变化
function generateSwitchPulse(duration, maxValue, switchType, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    let value = 0

    // 开关脉冲只在最开始的几个时间点出现
    if (i <= 2) { // 前200ms内的脉冲
      switch (switchType) {
        case 'intermediate_switch':
          value = i === 1 ? maxValue : 0 // 在第100ms时出现脉冲
          break
        case 'final_switch':
          value = i === 1 ? maxValue : 0 // 在第100ms时出现脉冲
          break
      }
    } else {
      value = 0 // 脉冲后保持为0
    }

    // 脉冲不需要噪声，保持干净的信号
    points.push({ time: time.toFixed(2), value: value.toFixed(2) })
  }

  return points
}

// 生成电场强度数据（多通道）- 电场建立阶段的实时数据
export function generateFieldStrengthData(progress = 1) {
  const channels = [
    { name: 'A', maxValue: 850000, color: '#00F0FF' },
    { name: 'B', maxValue: 920000, color: '#39FF14' },
    { name: 'C', maxValue: 780000, color: '#18FFFF' },
    { name: 'D', maxValue: 960000, color: '#7C4DFF' }
  ]

  return channels.map(channel => ({
    channel: channel.name,
    color: channel.color,
    currentValue: channel.maxValue * (0.8 + 0.2 * Math.random()), // 当前实时值
    data: generateFieldWaveform(5000, channel.maxValue, progress),
    unit: 'V/m'
  }))
}

// 生成电场波形数据
function generateFieldWaveform(duration, maxValue, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    // 电场强度随时间快速上升然后稳定，有高频振荡
    let value = maxValue * (1 - Math.exp(-5 * i / steps))
    // 添加高频振荡
    value += maxValue * 0.1 * Math.sin(20 * Math.PI * i / steps)
    // 添加噪声
    value += (Math.random() - 0.5) * 0.05 * maxValue

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成Marx建立波形数据 - 多通道可选择显示
export function generateMarxWaveform(progress = 1) {
  const channels = [
    { name: '通道1', maxValue: 45000000, color: '#00F0FF', visible: true },
    { name: '通道2', maxValue: 52000000, color: '#39FF14', visible: true },
    { name: '通道3', maxValue: 48000000, color: '#18FFFF', visible: true },
    { name: '通道4', maxValue: 55000000, color: '#7C4DFF', visible: true },
    { name: '通道5', maxValue: 43000000, color: '#E91E63', visible: false },
    { name: '通道6', maxValue: 49000000, color: '#CCFF00', visible: false },
    { name: '通道7', maxValue: 51000000, color: '#00FFFF', visible: false },
    { name: '通道8', maxValue: 47000000, color: '#8A2BE2', visible: false }
  ]

  return channels.map(channel => ({
    channel: channel.name,
    color: channel.color,
    visible: channel.visible,
    data: generateMarxChannelWaveform(5000, channel.maxValue, progress),
    unit: 'V'
  }))
}

// 生成Marx通道波形数据
function generateMarxChannelWaveform(duration, maxValue, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    // Marx发生器的典型波形：快速上升，然后振荡衰减
    let value = 0

    if (i < steps * 0.1) {
      // 前10%时间快速上升
      value = maxValue * (i / (steps * 0.1))
    } else {
      // 后90%时间振荡衰减
      const decayTime = (i - steps * 0.1) / (steps * 0.9)
      value = maxValue * Math.exp(-2 * decayTime) * (1 + 0.3 * Math.sin(10 * Math.PI * decayTime))
    }

    // 添加小幅噪声
    value += (Math.random() - 0.5) * 0.02 * maxValue

    points.push({ time: time.toFixed(2), value: Math.max(0, value.toFixed(2)) })
  }

  return points
}

// 生成信息报告摘要 - 电场建立阶段的详细报告
export function generateReportSummary() {
  return {
    direction: '垂直向上',
    area: '1.2 m²',
    riseTime: '2.3 μs',
    pulseWidth: '15.7 μs',
    amplitude: '85.6 MV/m',
    peakValue: '92.1 MV/m',
    peakTime: '8.9 μs',
    // 新增实时更新的数据
    realTimeData: {
      currentFieldStrength: (850 + Math.random() * 100).toFixed(1) + ' kV/m',
      antennaVoltage: (45 + Math.random() * 10).toFixed(1) + ' MV',
      antennaCurrent: (12 + Math.random() * 3).toFixed(1) + ' kA',
      energyReleased: (89 + Math.random() * 5).toFixed(1) + '%',
      experimentStatus: '电场建立中'
    }
  }
}

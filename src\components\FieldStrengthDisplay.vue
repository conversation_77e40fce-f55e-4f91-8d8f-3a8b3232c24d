<template>
  <div class="field-strength-display">
    <div class="display-header">
      <h3 class="display-title">实时电场强度监测</h3>
      <div class="channel-controls">
        <button 
          v-for="channel in channels" 
          :key="channel.channel"
          @click="toggleChannel(channel.channel)"
          class="channel-btn"
          :class="{ active: channel.visible }"
          :style="{ borderColor: channel.color }"
        >
          {{ channel.channel }}
        </button>
      </div>
    </div>
    
    <div class="field-content">
      <!-- 多通道数值显示 -->
      <div class="field-values">
        <div 
          v-for="channel in visibleChannels" 
          :key="channel.channel"
          class="value-item"
          :style="{ borderColor: channel.color }"
        >
          <div class="value-header">
            <span class="channel-name">通道{{ channel.channel }}</span>
            <span class="channel-status" :style="{ color: channel.color }">●</span>
          </div>
          <div class="channel-info">
            <div class="info-item">
              <span class="info-label">位置:</span>
              <span class="info-value">{{ channel.position }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">高度:</span>
              <span class="info-value">{{ channel.height }}</span>
            </div>
          </div>
          <div class="value-display">
            <span class="value-number" :style="{ color: channel.color }">
              {{ formatFieldValue(channel.currentValue) }}
            </span>
            <span class="value-unit">kV/m</span>
          </div>
          <div class="value-stats">
            <div class="stat-item">
              峰值: {{ formatFieldValue(channel.peakValue) }} kV/m
            </div>
            <div class="stat-item">
              状态: <span :style="{ color: channel.color }">{{ channel.status }}</span>
            </div>
            <div class="stat-item">
              信号质量: {{ channel.signalQuality }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 电场强度图表 -->
      <div class="field-chart" ref="chartContainer"></div>
    </div>
    
    <!-- 实时监测和环境数据 -->
    <div class="field-statistics">
      <div class="stat-group">
        <div class="stat-title">实时监测</div>
        <div class="stat-items">
          <div class="stat-item">
            <span class="stat-label">平均强度:</span>
            <span class="stat-value">{{ realTimeData.averageFieldStrength || '0 kV/m' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最大强度:</span>
            <span class="stat-value">{{ realTimeData.maxFieldStrength || '0 kV/m' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">均匀性:</span>
            <span class="stat-value">{{ realTimeData.fieldUniformity || '0%' }}</span>
          </div>
        </div>
      </div>

      <div class="stat-group">
        <div class="stat-title">环境参数</div>
        <div class="stat-items">
          <div class="stat-item">
            <span class="stat-label">温度:</span>
            <span class="stat-value">{{ environmentalData.temperature || '0°C' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">湿度:</span>
            <span class="stat-value">{{ environmentalData.humidity || '0%' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">风速:</span>
            <span class="stat-value">{{ environmentalData.windSpeed || '0 m/s' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  fieldData: {
    type: Object,
    default: () => ({
      channels: [],
      realTimeMonitoring: {},
      environmentalData: {},
      systemStatus: {}
    })
  }
})

// 响应式数据
const chartContainer = ref(null)
const channels = ref([])
let chartInstance = null

// 颜色配置
const channelColors = ['#00F0FF', '#39FF14', '#FFC400', '#FF4444', '#7C4DFF', '#18FFFF', '#E91E63', '#FF6B35']

// 计算属性
const visibleChannels = computed(() => {
  return channels.value.filter(channel => channel.visible)
})

const totalChannels = computed(() => {
  return channels.value.length
})

const activeChannelCount = computed(() => {
  return channels.value.filter(channel => channel.currentValue > 1000).length
})

const realTimeData = computed(() => {
  return props.fieldData.realTimeMonitoring || {}
})

const environmentalData = computed(() => {
  return props.fieldData.environmentalData || {}
})

const systemStatus = computed(() => {
  return props.fieldData.systemStatus || {}
})

// 方法
const formatFieldValue = (value) => {
  return (value / 1000).toFixed(1) // 转换为kV/m
}

const toggleChannel = (channelName) => {
  const channel = channels.value.find(c => c.channel === channelName)
  if (channel) {
    channel.visible = !channel.visible
    updateChart()
  }
}

const processFieldData = () => {
  if (!props.fieldData || !props.fieldData.channels || props.fieldData.channels.length === 0) return

  channels.value = props.fieldData.channels.map((channelData, index) => {
    const data = channelData.data || []
    const currentValue = parseFloat(channelData.currentValue) * 1000 || 0 // 转换为V/m
    const peakValue = parseFloat(channelData.peakValue) * 1000 || 0 // 转换为V/m

    return {
      channel: channelData.channel,
      position: channelData.position,
      height: channelData.height,
      data: data,
      currentValue: currentValue,
      peakValue: peakValue,
      color: channelData.color || channelColors[index % channelColors.length],
      visible: true,
      status: channelData.status,
      signalQuality: channelData.signalQuality
    }
  })
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    legend: {
      show: false
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'value',
      name: '时间(s)',
      nameTextStyle: { color: '#00F0FF', fontSize: 12 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { color: '#00F0FFCC', fontSize: 10 },
      splitLine: { lineStyle: { color: '#00F0FF20' } }
    },
    yAxis: {
      type: 'value',
      name: '电场强度(MV/m)',
      nameTextStyle: { color: '#00F0FF', fontSize: 12 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { 
        color: '#00F0FFCC', 
        fontSize: 10,
        formatter: (value) => (value / 1000000).toFixed(1)
      },
      splitLine: { lineStyle: { color: '#00F0FF20' } }
    },
    series: [],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: '#00F0FF',
      textStyle: { color: '#fff' },
      formatter: (params) => {
        let content = '<div style="padding: 5px;">'
        content += '<div style="color: #00F0FF; font-weight: bold; margin-bottom: 5px;">电场强度</div>'
        params.forEach(param => {
          const channel = channels.value.find(c => c.channel === param.seriesName)
          if (channel) {
            content += `<div style="color: ${channel.color};">`
            content += `${param.seriesName}: ${formatFieldValue(param.data[1])} MV/m`
            content += '</div>'
          }
        })
        content += '</div>'
        return content
      }
    }
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  const series = visibleChannels.value.map(channel => ({
    name: channel.channel,
    type: 'line',
    data: channel.data.map(item => [parseFloat(item.time), parseFloat(item.value)]),
    smooth: true,
    lineStyle: {
      color: channel.color,
      width: 2,
      shadowColor: channel.color,
      shadowBlur: 8
    },
    itemStyle: {
      color: channel.color
    },
    symbol: 'none',
    animation: true
  }))
  
  chartInstance.setOption({
    series: series
  })
}

// 监听数据变化
watch(() => props.fieldData, () => {
  processFieldData()
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  processFieldData()
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.field-strength-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 12px;
  box-sizing: border-box;
  overflow: hidden;
}

.display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
  flex-shrink: 0;
}

.display-title {
  font-size: 18px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
}

.channel-controls {
  display: flex;
  gap: 8px;
}

.channel-btn {
  padding: 6px 12px;
  border: 1px solid #666;
  background: transparent;
  color: #ccc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.channel-btn.active {
  background: rgba(0, 240, 255, 0.1);
  color: #fff;
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.3);
}

.channel-btn:hover {
  background: rgba(0, 240, 255, 0.05);
}

.field-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0;
  overflow: hidden;
}

.field-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 8px;
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.value-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid;
  border-radius: 6px;
  padding: 10px;
  transition: all 0.3s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
}

.value-item:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
}

.value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.channel-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}

.channel-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.info-value {
  font-size: 11px;
  color: #fff;
  font-weight: bold;
}

.channel-status {
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.value-number {
  font-size: 24px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 10px currentColor;
}

.value-unit {
  font-size: 12px;
  color: #ccc;
}

.value-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.value-stats .stat-item {
  font-size: 10px;
  color: #ccc;
  display: flex;
  justify-content: space-between;
}

.field-chart {
  flex: 1;
  min-height: 300px;
}

.field-statistics {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 240, 255, 0.3);
}

.stat-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-title {
  font-size: 14px;
  font-weight: bold;
  color: #39FF14;
}

.stat-items {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  font-size: 12px;
  color: #ccc;
}

.stat-value {
  font-size: 12px;
  font-weight: bold;
  color: #18FFFF;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .field-values {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .stat-items {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .display-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .channel-controls {
    flex-wrap: wrap;
  }
}
</style>

<template>
  <div class="marx-waveform">
    <div class="waveform-header">
      <h3 class="waveform-title">Marx建立波形 (多通道)</h3>
      <div class="channel-selector">
        <button 
          class="select-all-btn"
          @click="toggleAllChannels"
          :class="{ active: allChannelsVisible }"
        >
          {{ allChannelsVisible ? '隐藏全部' : '显示全部' }}
        </button>
      </div>
    </div>
    
    <div class="waveform-content">
      <!-- 通道控制面板 -->
      <div class="channel-controls">
        <div 
          v-for="channel in channels" 
          :key="channel.channel"
          class="channel-control"
          :class="{ active: channel.visible }"
        >
          <div class="control-header">
            <label class="channel-checkbox">
              <input 
                type="checkbox" 
                v-model="channel.visible"
                @change="updateChart"
              >
              <span class="checkbox-custom" :style="{ backgroundColor: channel.color }"></span>
              <span class="channel-name">{{ channel.channel }}</span>
            </label>
            <div class="channel-value" :style="{ color: channel.color }">
              {{ formatValue(channel.currentValue) }}
            </div>
          </div>
          <div class="channel-stats">
            <span class="stat-item">
              峰值: {{ formatValue(channel.peakValue) }}
            </span>
            <span class="stat-item">
              平均: {{ formatValue(channel.avgValue) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Marx波形图表 -->
      <div class="marx-chart" ref="chartContainer"></div>
    </div>
    
    <!-- 波形分析 -->
    <div class="waveform-analysis">
      <div class="analysis-section">
        <div class="section-title">波形分析</div>
        <div class="analysis-grid">
          <div class="analysis-item">
            <div class="analysis-label">同步性</div>
            <div class="analysis-value good">优秀</div>
          </div>
          <div class="analysis-item">
            <div class="analysis-label">一致性</div>
            <div class="analysis-value good">良好</div>
          </div>
          <div class="analysis-item">
            <div class="analysis-label">稳定性</div>
            <div class="analysis-value excellent">优秀</div>
          </div>
          <div class="analysis-item">
            <div class="analysis-label">活跃通道</div>
            <div class="analysis-value">{{ activeChannelCount }}/{{ totalChannels }}</div>
          </div>
        </div>
      </div>
      
      <div class="analysis-section">
        <div class="section-title">统计信息</div>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ maxAmplitude }}</div>
            <div class="stat-label">最大幅值 (MV)</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ avgAmplitude }}</div>
            <div class="stat-label">平均幅值 (MV)</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ riseTime }}</div>
            <div class="stat-label">上升时间 (μs)</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  marxData: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const chartContainer = ref(null)
const channels = ref([])
let chartInstance = null

// 颜色配置
const channelColors = [
  '#00F0FF', '#39FF14', '#FFC400', '#FF4444', 
  '#7C4DFF', '#18FFFF', '#E91E63', '#FF6B35'
]

// 计算属性
const visibleChannels = computed(() => {
  return channels.value.filter(channel => channel.visible)
})

const totalChannels = computed(() => {
  return channels.value.length
})

const activeChannelCount = computed(() => {
  return channels.value.filter(channel => channel.currentValue > 1000000).length // 大于1MV
})

const allChannelsVisible = computed(() => {
  return channels.value.length > 0 && channels.value.every(channel => channel.visible)
})

const maxAmplitude = computed(() => {
  if (channels.value.length === 0) return '0.0'
  const max = Math.max(...channels.value.map(channel => channel.peakValue))
  return (max / 1000000).toFixed(1) // 转换为MV
})

const avgAmplitude = computed(() => {
  if (channels.value.length === 0) return '0.0'
  const sum = channels.value.reduce((acc, channel) => acc + channel.avgValue, 0)
  const avg = sum / channels.value.length
  return (avg / 1000000).toFixed(1) // 转换为MV
})

const riseTime = computed(() => {
  // 模拟计算上升时间
  return '2.3'
})

// 方法
const formatValue = (value) => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)} MV`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)} KV`
  }
  return `${value.toFixed(0)} V`
}

const toggleAllChannels = () => {
  const newVisibility = !allChannelsVisible.value
  channels.value.forEach(channel => {
    channel.visible = newVisibility
  })
  updateChart()
}

const processMarxData = () => {
  if (!props.marxData || props.marxData.length === 0) return
  
  channels.value = props.marxData.map((channelData, index) => {
    const data = channelData.data || []
    const currentValue = data.length > 0 ? parseFloat(data[data.length - 1]?.value || 0) : 0
    const peakValue = data.length > 0 ? Math.max(...data.map(item => parseFloat(item.value))) : 0
    const avgValue = data.length > 0 ? 
      data.reduce((acc, item) => acc + parseFloat(item.value), 0) / data.length : 0
    
    return {
      channel: channelData.channel,
      data: data,
      currentValue: currentValue,
      peakValue: peakValue,
      avgValue: avgValue,
      color: channelColors[index % channelColors.length],
      visible: channelData.visible !== undefined ? channelData.visible : true
    }
  })
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    legend: {
      show: false
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '8%',
      bottom: '12%'
    },
    xAxis: {
      type: 'value',
      name: '时间(s)',
      nameTextStyle: { color: '#00F0FF', fontSize: 12 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { color: '#00F0FFCC', fontSize: 10 },
      splitLine: { lineStyle: { color: '#00F0FF15' } }
    },
    yAxis: {
      type: 'value',
      name: '电压(MV)',
      nameTextStyle: { color: '#00F0FF', fontSize: 12 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { 
        color: '#00F0FFCC', 
        fontSize: 10,
        formatter: (value) => (value / 1000000).toFixed(0)
      },
      splitLine: { lineStyle: { color: '#00F0FF15' } }
    },
    series: [],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: '#00F0FF',
      textStyle: { color: '#fff' },
      formatter: (params) => {
        let content = '<div style="padding: 5px;">'
        content += '<div style="color: #00F0FF; font-weight: bold; margin-bottom: 5px;">Marx波形</div>'
        content += `<div>时间: ${params[0].data[0]}s</div>`
        params.forEach(param => {
          const channel = channels.value.find(c => c.channel === param.seriesName)
          if (channel) {
            content += `<div style="color: ${channel.color};">`
            content += `${param.seriesName}: ${formatValue(param.data[1])}`
            content += '</div>'
          }
        })
        content += '</div>'
        return content
      }
    },
    dataZoom: [{
      type: 'inside',
      xAxisIndex: 0,
      filterMode: 'none'
    }, {
      type: 'slider',
      xAxisIndex: 0,
      height: 20,
      bottom: 10,
      handleStyle: {
        color: '#00F0FF'
      },
      textStyle: {
        color: '#00F0FF'
      }
    }]
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  const series = visibleChannels.value.map(channel => ({
    name: channel.channel,
    type: 'line',
    data: channel.data.map(item => [parseFloat(item.time), parseFloat(item.value)]),
    smooth: true,
    lineStyle: {
      color: channel.color,
      width: 2,
      shadowColor: channel.color,
      shadowBlur: 6
    },
    itemStyle: {
      color: channel.color
    },
    symbol: 'none',
    animation: true,
    animationDuration: 1000
  }))
  
  chartInstance.setOption({
    series: series
  })
}

// 监听数据变化
watch(() => props.marxData, () => {
  processMarxData()
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  processMarxData()
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.marx-waveform {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 12px;
  box-sizing: border-box;
  overflow: hidden;
}

.waveform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(24, 255, 255, 0.3);
  flex-shrink: 0;
}

.waveform-title {
  font-size: 18px;
  font-weight: bold;
  color: #18FFFF;
  margin: 0;
}

.select-all-btn {
  padding: 8px 16px;
  border: 1px solid #18FFFF;
  background: transparent;
  color: #18FFFF;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.select-all-btn.active,
.select-all-btn:hover {
  background: rgba(24, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(24, 255, 255, 0.3);
}

.waveform-content {
  flex: 1;
  display: flex;
  gap: 12px;
  min-height: 0;
  overflow: hidden;
}

.channel-controls {
  width: 160px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-shrink: 0;
  overflow-y: auto;
  padding-right: 4px;
}

.channel-control {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  transition: all 0.3s ease;
  min-height: 60px;
}

.channel-control.active {
  border-color: rgba(24, 255, 255, 0.5);
  background: rgba(24, 255, 255, 0.05);
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.channel-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.channel-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #666;
  transition: all 0.3s ease;
}

.channel-checkbox input[type="checkbox"]:checked + .checkbox-custom {
  border-color: currentColor;
  box-shadow: 0 0 8px currentColor;
}

.channel-name {
  font-size: 12px;
  color: #fff;
  font-weight: bold;
}

.channel-value {
  font-size: 11px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.channel-stats {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.stat-item {
  font-size: 10px;
  color: #ccc;
}

.marx-chart {
  flex: 1;
  min-height: 0;
  height: 100%;
}

.waveform-analysis {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(24, 255, 255, 0.3);
}

.analysis-section {
  flex: 1;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #39FF14;
  margin-bottom: 10px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
}

.analysis-label {
  font-size: 11px;
  color: #ccc;
}

.analysis-value {
  font-size: 11px;
  font-weight: bold;
}

.analysis-value.excellent {
  color: #39FF14;
}

.analysis-value.good {
  color: #18FFFF;
}

.stats-grid {
  display: flex;
  gap: 15px;
}

.stat-card {
  flex: 1;
  text-align: center;
  padding: 12px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #FFC400;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 10px;
  color: #ccc;
}

/* 滚动条样式 */
.channel-controls::-webkit-scrollbar {
  width: 4px;
}

.channel-controls::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.channel-controls::-webkit-scrollbar-thumb {
  background: #18FFFF;
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .waveform-content {
    flex-direction: column;
  }
  
  .channel-controls {
    width: 100%;
    max-height: 150px;
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
  }
  
  .channel-control {
    min-width: 150px;
  }
  
  .waveform-analysis {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    flex-direction: column;
  }
}
</style>
